import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivative/derivatives_portfolio/derivatives_portfolio_cubit.dart';
import 'package:vp_trading/screen/derivatives_portfolio/open_positions/open_positions_list_widget.dart';
  
class DerivativesPortfolioScreen extends StatefulWidget {
  const DerivativesPortfolioScreen({super.key});

  @override
  State<DerivativesPortfolioScreen> createState() =>
      _DerivativesPortfolioScreenState();
}

class _DerivativesPortfolioScreenState extends State<DerivativesPortfolioScreen>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;
  late final DerivativesPortfolioCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = DerivativesPortfolioCubit(
      accountId: 'FDS0001005921',
      symbol: 'ALL',
    );
    _tabController = TabController(length: 2, vsync: this, initialIndex: 0);
    _tabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    // Handle tab changes if needed
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<DerivativesPortfolioCubit>(
      create: (context) => _cubit,
      child: VPScaffold(
        appBar: VPAppBar.layer(
          backgroundColor: vpColor.backgroundElevation0,
          title: "Danh mục",
        ),
        body: SafeArea(
          child: Column(children: [Expanded(child: _buildTabBarView())]),
        ),
      ),
    );
  }

  /// Builds the tab bar view with open and closed positions
  Widget _buildTabBarView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ColoredBox(
          color: vpColor.backgroundElevation0,
          child: VPTabBar(
            controller: _tabController,
            tabs: const [Tab(text: "Vị thế mở"), Tab(text: "Vị thế đóng")],
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            physics: const RangeMaintainingScrollPhysics(),
            children: [
              // Open positions tab with conditional UI
              const OpenPositionsListWidget(),
              // Closed positions tab (placeholder for now)
              _buildClosedPositionsTab(),
            ],
          ),
        ),
      ],
    );
  }

  /// Builds the closed positions tab (placeholder)
  Widget _buildClosedPositionsTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 64, color: vpColor.iconSecondary),
          const SizedBox(height: 16),
          Text(
            'Vị thế đóng',
            style: context.textStyle.subtitle16?.copyWith(
              color: vpColor.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Danh sách các vị thế đã đóng sẽ hiển thị ở đây',
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
