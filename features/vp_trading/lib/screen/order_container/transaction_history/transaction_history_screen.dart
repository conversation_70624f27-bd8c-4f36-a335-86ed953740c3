import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/screens/accoun_list/bottom_sheet_account_list/money_sub_account_bottom_sheet.dart';
import 'package:vp_trading/cubit/order_container/order_container/order_container_cubit.dart';
import 'package:vp_trading/cubit/order_container/transaction_history/order_transaction_history_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/request/order/filter_nomal_param.dart';
import 'package:vp_trading/screen/order_container/enum/enum_tabbar_order.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/bottom_filter_status_order.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/normal_order_title_widget.dart';
import 'package:vp_trading/screen/order_container/transaction_history/widget/transaction_history_item_widget.dart';
import 'package:vp_trading/screen/order_container/widget/bottom_filter_order_container.dart';
import 'package:vp_trading/widgets/command_history_loading_widget.dart';

class TransactionHistoryPage extends StatefulWidget {
  const TransactionHistoryPage({super.key});

  @override
  State<TransactionHistoryPage> createState() => _TransactionHistoryPageState();
}

class _TransactionHistoryPageState extends State<TransactionHistoryPage>
    with AutomaticKeepAliveClientMixin {
  late OrderTransactionHistoryCubit _orderTransactionHistoryCubit;
  FilterNormalParam? _lastAppliedFilter;
  @override
  void initState() {
    super.initState();
    _orderTransactionHistoryCubit = OrderTransactionHistoryCubit()..init();
  }

  @override
  void dispose() {
    _orderTransactionHistoryCubit.close();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider.value(
      value: _orderTransactionHistoryCubit,
      child: MultiBlocListener(
        listeners: [
          BlocListener<OrderContainerCubit, OrderContainerState>(
            listener: (context, state) async {
              if (state.currentTabbar == EnumTabbarOrder.transactionHistory) {
                final newFilter = state.currentFilterParam;
                if (newFilter != null && newFilter != _lastAppliedFilter) {
                  _lastAppliedFilter = newFilter;
                  await _orderTransactionHistoryCubit.updateFilter(newFilter);
                }
              }
            },
          ),
          BlocListener<
            OrderTransactionHistoryCubit,
            OrderTransactionHistoryState
          >(
            listener: (context, state) {
              if (state.errorMessage != null) {
                context.showSnackBar(
                  content: state.errorMessage ?? "-",
                  snackBarType: VPSnackBarType.error,
                );
                context
                    .read<OrderTransactionHistoryCubit>()
                    .resetErrorMessage();
              }
            },
          ),
        ],
        child: BlocBuilder<
          OrderTransactionHistoryCubit,
          OrderTransactionHistoryState
        >(
          builder: (context, state) {
            return Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Column(
                children: [
                  _filterDropdown(state, context),
                  const SizedBox(height: 16),
                  if (state.status.isLoading)
                    const Expanded(child: CommandHistoryLoadingWidget()),
                  if (!state.status.isLoading && state.listItems.isEmpty)
                    Expanded(
                      child: NoDataView(
                        content:
                            VPTradingLocalize.of(
                              context,
                            ).trading_no_data_message,
                      ),
                    ),
                  if (state.listItems.isNotEmpty &&
                      !state.status.isLoading) ...[
                    const ConditionNormalTitle(
                      expandTitleWidget: [10, 6, 7, 11],
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: PullToRefreshView(
                        onRefresh: () async {
                          await _orderTransactionHistoryCubit.loadData();
                        },
                        child: ListViewHelper.separated(
                          physics: const AlwaysScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            return TransactionHistoryItem(
                              model: state.listItems[index],
                              accountId:
                                  state.filterParam?.subAccountModel?.id ?? "",
                            );
                          },
                          itemCount: () => state.listItems.length,
                          hasMore: () => state.hasMore,
                          loadMore:
                              () => _orderTransactionHistoryCubit.loadMore(),
                          refresh: () async {
                            await _orderTransactionHistoryCubit.loadData();
                          },
                          separatorBuilder:
                              (context, index) => const SizedBox(height: 8),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _filterDropdown(
    OrderTransactionHistoryState state,
    BuildContext context,
  ) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: VPDropdownView.small(
            hint: VPTradingLocalize.current.trading_sub_account,
            width: double.infinity,
            textStyle: context.textStyle.captionRegular?.copyWith(
              color: vpColor.textPrimary,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            value:
                state
                    .filterParam
                    ?.subAccountModel
                    ?.subAccountProductTypeCdEnum
                    .displayName,
            onTap: () async => _showSubAccountBottomSheet(context),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          flex: 1,
          child: VPDropdownView.small(
            hint: VPTradingLocalize.current.trading_status,
            width: double.infinity,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            textStyle: context.textStyle.captionRegular?.copyWith(
              color: vpColor.textPrimary,
            ),
            value: OrderStatusEnumExt.displayNameFilter(
              state.filterParam?.orderStatus ?? [],
            ),
            onTap: () async => _showFilterStatusOrder(context),
          ),
        ),
        const SizedBox(width: 8),
        _buildFilter(),
      ],
    );
  }

  Widget _buildFilter() {
    return BlocBuilder<OrderContainerCubit, OrderContainerState>(
      builder: (context, state) {
        final hasFilter = state.hasFilter;
        return GestureDetector(
          onTap: _showFilterOrderContainer,
          child: DesignAssets.icons.icFilter.svg(
            colorFilter: ColorFilter.mode(
              hasFilter ? vpColor.textBrand : vpColor.iconPrimary,
              BlendMode.srcIn,
            ),
            height: 20,
          ),
        );
      },
    );
  }

  void _showFilterOrderContainer() {
    final currentFilter =
        context.read<OrderContainerCubit>().state.currentFilterParam;
    final currentTabbar =
        context.read<OrderContainerCubit>().state.currentTabbar;
    openFilterOrderContainerBottomSheet(
      context,
      initialOrderType: currentFilter?.orderType,
      initialSymbol: currentFilter?.symbol,
      initialTimeFilter: currentFilter?.timeFilter,
      dateTimeRangeCustom: currentFilter?.dateTimeRangeCustom,
      onApply: (orderTypeEnum, symbol, dateTimeRangeCustom, timeFilterEnum) {
        final filterParam = FilterNormalParam(
          orderType: orderTypeEnum,
          symbol: symbol?.trim().toUpperCase(),
          dateTimeRangeCustom: dateTimeRangeCustom,
          timeFilter:
              currentTabbar == EnumTabbarOrder.transactionHistory
                  ? timeFilterEnum
                  : null,
        );
        context.read<OrderContainerCubit>().updateCurrentFilter(filterParam);
      },
    );
  }

  Future<void> _showSubAccountBottomSheet(BuildContext context) async {
    final listSubAccounts = GetIt.instance<SubAccountCubit>().subAccountsStock;
    final result = await showDerivativeSubAccountBottomSheet(
      context: context,
      listSubAccounts: listSubAccounts,
    );
    if (result is! SubAccountModel || !context.mounted) return;
    await _orderTransactionHistoryCubit.updateFilterAccount(result);
  }

  void _showFilterStatusOrder(BuildContext context) {
    var orderStatus =
        _orderTransactionHistoryCubit.state.filterParam?.orderStatus;
    openFilterStatusOrderBottomSheet(
      context: context,
      listStatus: orderStatus,
      onApply: (p0) async {
        await _orderTransactionHistoryCubit.updateFilterStatus(p0);
      },
    );
  }
}
