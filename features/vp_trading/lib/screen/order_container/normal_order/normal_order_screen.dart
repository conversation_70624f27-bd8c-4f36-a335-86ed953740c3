import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/order_container/normal_order/normal_order_cubit.dart';
import 'package:vp_trading/cubit/order_container/order_container/order_container_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/request/order/filter_nomal_param.dart';
import 'package:vp_trading/screen/order_container/enum/enum_tabbar_order.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/bottom_filter_status_order.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/normal_order_list.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/normal_order_title_widget.dart';
import 'package:vp_trading/screen/order_container/widget/bottom_filter_order_container.dart';
import 'package:vp_trading/widgets/command_history_loading_widget.dart';

class NormalOrderScreen extends StatefulWidget {
  const NormalOrderScreen({super.key});

  @override
  State<NormalOrderScreen> createState() => _NormalOrderScreenState();
}

class _NormalOrderScreenState extends State<NormalOrderScreen>
    with AutomaticKeepAliveClientMixin {
  late NormalOrderCubit _normalOrderCubit;
  FilterNormalParam? _lastAppliedFilter;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _normalOrderCubit = NormalOrderCubit()..init();
  }

  @override
  void dispose() {
    _normalOrderCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider.value(
      value: _normalOrderCubit,
      child: MultiBlocListener(
        listeners: [
          BlocListener<OrderContainerCubit, OrderContainerState>(
            listener: (context, state) async {
              if (state.currentTabbar == EnumTabbarOrder.normal) {
                final newFilter = state.currentFilterParam;
                if (newFilter != null && newFilter != _lastAppliedFilter) {
                  _lastAppliedFilter = newFilter;
                  await _normalOrderCubit.updateFilter(newFilter);
                }
              }
            },
          ),
          BlocListener<NormalOrderCubit, NormalOrderState>(
            listener: (context, state) {
              if (state.errorMessage != null) {
                context.showSnackBar(
                  content: state.errorMessage ?? "-",
                  snackBarType: VPSnackBarType.error,
                );
                context.read<NormalOrderCubit>().resetErrorMessage();
              }
            },
          ),
        ],
        child: BlocBuilder<NormalOrderCubit, NormalOrderState>(
          builder: (context, state) {
            return Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Column(
                children: [
                  _filtterDropdown(state, context),
                  const SizedBox(height: 16),
                  if (state.isLoading)
                    const Expanded(child: CommandHistoryLoadingWidget()),
                  if (!state.isLoading && state.listItems.isEmpty)
                    Expanded(
                      child: PullToRefreshView(
                        onRefresh: () async {
                          await _normalOrderCubit.loadData();
                        },
                        child: NoDataView(
                          content:
                              VPTradingLocalize.current.trading_no_data_message,
                        ),
                      ),
                    ),
                  if (state.listItems.isNotEmpty && !state.isLoading) ...[
                    ConditionNormalTitle(
                      expandTitleWidget: const [10, 6, 7, 12],
                      showTitleDeleteAll: true,
                      onDeleteAll: () {
                        // todo: cancel all order
                      },
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: PullToRefreshView(
                        onRefresh: () async {
                          await _normalOrderCubit.loadData();
                        },
                        child: NormalOrderList(
                          items: state.listItems,
                          hasMore: state.hasMore,
                          loadMore: () => _normalOrderCubit.loadMore(),
                          refresh: () async {
                            await _normalOrderCubit.loadData();
                          },
                          editSuccess: () async {
                            await _normalOrderCubit.loadData();
                          },
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _filtterDropdown(NormalOrderState state, BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: VPDropdownView.small(
            hint: VPTradingLocalize.current.trading_sub_account_hint,
            width: double.infinity,
            textStyle: context.textStyle.captionRegular?.copyWith(
              color: vpColor.textPrimary,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            value:
                state
                    .filterParam
                    ?.subAccountModel
                    ?.subAccountProductTypeCdEnum
                    .displayName,
            onTap: () async => _showSubAccountBottomSheet(context),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          flex: 1,
          child: VPDropdownView.small(
            hint: VPTradingLocalize.current.trading_status_hint,
            width: double.infinity,
            overflow: TextOverflow.ellipsis,
            textStyle: context.textStyle.captionRegular?.copyWith(
              color: vpColor.textPrimary,
            ),
            maxLines: 1,
            value: OrderStatusEnumExt.displayNameFilter(
              state.filterParam?.orderStatus ?? [],
            ),
            onTap: () async => _showFilterStatusOrder(context),
          ),
        ),
        const SizedBox(width: 8),
        _buildFilter(),
      ],
    );
  }

  Widget _buildFilter() {
    return BlocBuilder<OrderContainerCubit, OrderContainerState>(
      builder: (context, state) {
        final hasFilter = state.hasFilter;
        return GestureDetector(
          onTap: _showFilterOrderContainer,
          child: DesignAssets.icons.icFilter.svg(
            colorFilter: ColorFilter.mode(
              hasFilter ? vpColor.textBrand : vpColor.iconPrimary,
              BlendMode.srcIn,
            ),
            height: 20,
          ),
        );
      },
    );
  }

  void _showFilterOrderContainer() {
    final currentFilter =
        context.read<OrderContainerCubit>().state.currentFilterParam;
    final currentTabbar =
        context.read<OrderContainerCubit>().state.currentTabbar;
    openFilterOrderContainerBottomSheet(
      context,
      initialOrderType: currentFilter?.orderType,
      initialSymbol: currentFilter?.symbol,
      initialTimeFilter: currentFilter?.timeFilter,
      dateTimeRangeCustom: currentFilter?.dateTimeRangeCustom,
      onApply: (orderTypeEnum, symbol, dateTimeRangeCustom, timeFilterEnum) {
        final filterParam = FilterNormalParam(
          orderType: orderTypeEnum,
          symbol: symbol?.trim().toUpperCase(),
          dateTimeRangeCustom: dateTimeRangeCustom,
          timeFilter:
              currentTabbar == EnumTabbarOrder.transactionHistory
                  ? timeFilterEnum
                  : null,
        );
        context.read<OrderContainerCubit>().updateCurrentFilter(filterParam);
      },
    );
  }

  Future<void> _showSubAccountBottomSheet(BuildContext context) async {
    final listSubAccounts = GetIt.instance<SubAccountCubit>().subAccountsStock;
    final result = await showDerivativeSubAccountBottomSheet(
      context: context,
      listSubAccounts: listSubAccounts,
    );
    if (result is! SubAccountModel || !context.mounted) return;
    await _normalOrderCubit.updateFilterAccount(result);
  }

  void _showFilterStatusOrder(BuildContext context) {
    var orderStatus = _normalOrderCubit.state.filterParam?.orderStatus;
    openFilterStatusOrderBottomSheet(
      context: context,
      listStatus: orderStatus,
      onApply: (p0) async {
        await _normalOrderCubit.updateFilterStatus(p0);
      },
    );
  }
}
