import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:marquee/marquee.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/stock_price_limit_view.dart';
import 'package:vp_trading/screen/place_order/widgets/stock_volume_value_view.dart';
import 'package:vp_trading/screen/place_order/widgets/symbol_search/symbol_search_screen.dart';
import 'package:vp_trading/widgets/buy_sell_view.dart';

double transform(double begin, double end, double t, [double x = 1]) {
  return Tween<double>(
    begin: begin,
    end: end,
  ).transform(x == 1 ? t : min(1.0, t * x));
}

Color transformColor(Color? begin, Color? end, double t, [double x = 1]) {
  return ColorTween(
        begin: begin,
        end: end,
      ).transform(x == 1 ? t : min(1.0, t * x)) ??
      Colors.transparent;
}

class MySliverPersistentHeader extends SliverPersistentHeaderDelegate {
  MySliverPersistentHeader({required this.minExtent, required this.maxExtent});

  @override
  final double minExtent;

  @override
  final double maxExtent;

  double hPadding = 16;
  double vPadding = 8;
  double vSpace = 8;

  double priceLimitHeight = 40;
  double buySellButtonHeight = 40;
  double icSearchSize = 24;

  double get deltaExtent => maxExtent - minExtent;

  void changeStock(BuildContext context) async {
    final isSell =
        context.read<PlaceOrderCubit>().state.action == OrderAction.sell;
    final result = await showChooseStockBottomSheet(context, isSell);
    if (result is String) {
      context.read<PlaceOrderCubit>().onSymbolChange(result);
    }
  }

  Future<void> _showSubAccountBottomSheet(BuildContext context) async {
    final listSubAccounts = GetIt.instance<SubAccountCubit>().subAccountsStock;
    final result = await showDerivativeSubAccountBottomSheet(
      context: context,
      listSubAccounts: listSubAccounts,
    );
    if (result is! SubAccountModel || !context.mounted) return;
    context.read<PlaceOrderCubit>().updateSupAccount(result.accountType);
  }

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    final currentExtent = max(minExtent, maxExtent - shrinkOffset);
    double t = clampDouble(
      1.0 - (currentExtent - minExtent) / deltaExtent,
      0,
      1,
    );

    return Container(
      color: vpColor.backgroundElevation0,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final maxWidth = constraints.maxWidth - (hPadding * 2);

          return SizedBox(
            height: double.infinity,
            child: Stack(
              children: [
                Positioned(
                  bottom: maxExtent - (buySellButtonHeight + vPadding),
                  left: hPadding,
                  right: hPadding,
                  child: AnimatedOpacity(
                    opacity: max(0, 1 - (4 * t)),
                    duration: const Duration(milliseconds: 200),
                    child: Row(
                      spacing: 8,
                      children: [
                        const Expanded(child: BuySellButtonView()),

                        Expanded(
                          child: BlocBuilder<PlaceOrderCubit, PlaceOrderState>(
                            builder: (context, state) {
                              return VPDropdownView.large(
                                value: state.subAccountType.shortName,
                                onTap: () async {
                                  _showSubAccountBottomSheet(context);
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                Positioned(
                  left: hPadding,
                  right: hPadding,
                  bottom: vPadding,
                  child: Row(
                    children: [
                      Transform.translate(
                        offset: const Offset(-10, 0),
                        child: IconButton(
                          onPressed: () {},
                          icon: const Icon(Icons.traffic_outlined),
                        ),
                      ),

                      const Expanded(flex: 2, child: StockVolumeValueView()),

                      const SizedBox(width: 10),

                      const Expanded(flex: 3, child: SizedBox.shrink()),
                    ],
                  ),
                ),

                Positioned(
                  left: 0,
                  top: 0,
                  right: 0,
                  child: IgnorePointer(
                    ignoring: true,
                    child: AnimatedOpacity(
                      opacity: min(1, 6 * t),
                      duration: const Duration(milliseconds: 200),
                      child: Container(
                        height: minExtent,
                        color: vpColor.backgroundElevation0,
                      ),
                    ),
                  ),
                ),

                Positioned(
                  left: hPadding,
                  right: hPadding,
                  bottom: transform(vPadding - 2, 5, t),
                  child: Row(
                    children: [
                      Visibility(
                        visible: false,
                        maintainAnimation: true,
                        maintainSize: true,
                        maintainState: true,
                        child: Transform.translate(
                          offset: const Offset(-10, 0),
                          child: IconButton(
                            onPressed: () {},
                            icon: const Icon(null),
                          ),
                        ),
                      ),

                      const Expanded(flex: 2, child: SizedBox.shrink()),

                      const SizedBox(width: 10),

                      const Expanded(flex: 3, child: StockPriceLimitView()),
                    ],
                  ),
                ),

                Positioned(
                  left: hPadding,
                  bottom:
                      maxExtent -
                      (buySellButtonHeight + vSpace + vPadding + icSearchSize),
                  child: GestureDetector(
                    onTap: () {
                      changeStock(context);
                    },
                    child: AnimatedOpacity(
                      opacity: max(0, 1 - (8 * t)),
                      duration: const Duration(milliseconds: 200),
                      child: DesignAssets.icons.icSearch.svg(
                        color: vpColor.iconPrimary,
                        width: icSearchSize,
                        height: icSearchSize,
                      ),
                    ),
                  ),
                ),

                Positioned(
                  left: transform(
                    hPadding + icSearchSize + 8,
                    hPadding,
                    t * 3,
                    3,
                  ),
                  top:
                      constraints.maxHeight > maxExtent
                          ? null
                          : transform(
                            buySellButtonHeight + vSpace + vPadding,
                            0,
                            t * 3,
                            3,
                          ),
                  child: GestureDetector(
                    onTap: () {
                      changeStock(context);
                    },
                    child: Row(
                      children: [
                        BlocSelector<PlaceOrderCubit, PlaceOrderState, String?>(
                          selector: (state) => state.symbol,
                          builder: (context, symbol) {
                            return Text(
                              symbol ?? '',
                              style: vpTextStyle.subtitle16
                                  .copyColor(vpColor.textPrimary)
                                  ?.copyWith(
                                    fontSize: transform(16, 24, t * 3, 3),
                                  ),
                            );
                          },
                        ),

                        BlocSelector<
                          StockInfoCubit,
                          StockInfoState,
                          StockInfoModel?
                        >(
                          selector: (state) => state.stockInfo,
                          builder: (context, stock) {
                            final marketCode = stock?.marketCode;

                            if (marketCode == null)
                              return const SizedBox.shrink();

                            return Text(
                              ' (${marketCode.name})',
                              style: vpTextStyle.body14
                                  .copyColor(vpColor.textPrimary)
                                  ?.copyWith(
                                    fontSize: transform(14, 20, t * 3, 3),
                                  ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  width: maxWidth / 2,
                  left: hPadding,
                  bottom:
                      maxExtent -
                      buySellButtonHeight -
                      vPadding -
                      vSpace -
                      icSearchSize -
                      24,
                  child: AnimatedOpacity(
                    opacity: max(0, 1 - (2 * t)),
                    duration: const Duration(milliseconds: 200),
                    child: BlocSelector<
                      StockInfoCubit,
                      StockInfoState,
                      StockInfoModel?
                    >(
                      selector: (state) => state.stockInfo,
                      builder: (context, stock) {
                        if (stock == null) return const SizedBox.shrink();
                        return GestureDetector(
                          onTap: () {
                            changeStock(context);
                          },
                          child: SizedBox(
                            width: 200,
                            height: 20,
                            child: Marquee(
                              blankSpace: 20,
                              text: stock.stockName,
                              velocity: 50.0,
                              style: vpTextStyle.body14.copyColor(
                                vpColor.textPrimary,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),

                /// closePrice
                Positioned(
                  left: transform(
                    maxWidth - (2 * hPadding) - 80,
                    hPadding,
                    t * 3,
                    3,
                  ),
                  top:
                      constraints.maxHeight > maxExtent
                          ? null
                          : transform(
                            buySellButtonHeight +
                                vPadding +
                                vSpace +
                                (40 - 32) / 2,
                            40,
                            t * 3,
                            3,
                          ),
                  child: ClosePriceView(t: t),
                ),

                Positioned(
                  left:
                      t != 0
                          ? transform(
                            maxWidth - hPadding,
                            hPadding + 40,
                            t * 3,
                            3,
                          )
                          : null,
                  right: t == 0 ? hPadding : null,
                  top:
                      constraints.maxHeight > maxExtent
                          ? null
                          : transform(
                            buySellButtonHeight + vPadding + vSpace,
                            40,
                            t * 3,
                            3,
                          ),
                  child: const PriceChangeView(),
                ),

                /// percent change
                Positioned(
                  left:
                      t != 0
                          ? transform(
                            maxWidth - hPadding,
                            hPadding + 80,
                            t * 3,
                            3,
                          )
                          : null,
                  right: t == 0 ? hPadding : null,
                  top:
                      constraints.maxHeight > maxExtent
                          ? null
                          : transform(
                            buySellButtonHeight + vPadding + vSpace + 20,
                            40,
                            t * 3,
                            3,
                          ),
                  child: const PercentChangeView(),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return minExtent != oldDelegate.minExtent ||
        maxExtent != oldDelegate.maxExtent;
  }
}

class PercentChangeView extends StatelessWidget {
  const PercentChangeView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StockInfoCubit, StockInfoState>(
      buildWhen: (preState, state) => preState.stockInfo != state.stockInfo,
      builder: (context, state) {
        final stock = state.stockInfo;

        if (stock != null) {
          return VPPercentChangeItemView(
            stock: stock,
            percentBuilder: (percent, closePrice) {
              if (closePrice == null) return '0.0%';

              return FormatUtils.formatPercent(percent);
            },
            styleBuilder: (percent, closePrice, color) {
              if (closePrice == null) {
                return vpTextStyle.captionMedium.copyColor(
                  vpColor.textPriceYellow,
                );
              }

              return vpTextStyle.captionMedium.copyColor(
                color ?? vpColor.textPriceYellow,
              );
            },
          );
        }

        return Text(
          '0.0',
          style: vpTextStyle.captionMedium.copyColor(vpColor.textPriceYellow),
        );
      },
    );
  }
}

class PriceChangeView extends StatelessWidget {
  const PriceChangeView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StockInfoCubit, StockInfoState>(
      buildWhen: (preState, state) => preState.stockInfo != state.stockInfo,
      builder: (context, state) {
        final stock = state.stockInfo;

        if (stock != null) {
          return VPPriceChangeItemView(
            stock: stock,
            changeBuilder: (change, closePrice) {
              if (closePrice == null) return '0.0';

              return FormatUtils.formatClosePrice(
                change,
                showSign: true,
                trimZero: false,
              );
            },
            styleBuilder: (percent, closePrice, color) {
              if (closePrice == null) {
                return vpTextStyle.captionMedium.copyColor(
                  vpColor.textPriceYellow,
                );
              }

              return vpTextStyle.captionMedium.copyColor(
                color ?? vpColor.textPriceYellow,
              );
            },
          );
        }

        return Text(
          '0.0',
          style: vpTextStyle.captionMedium.copyColor(vpColor.textPriceYellow),
        );
      },
    );
  }
}

class ClosePriceView extends StatelessWidget {
  const ClosePriceView({required this.t, super.key});

  final num t;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StockInfoCubit, StockInfoState>(
      buildWhen: (preState, state) => preState.stockInfo != state.stockInfo,
      builder: (context, state) {
        final stock = state.stockInfo;

        if (stock == null) return const SizedBox.shrink();

        final placeHolderColor = vpTextStyle.headineBold5
            .copyColor(vpColor.textPriceYellow)
            ?.copyWith(fontSize: transform(24, 12, t * 3, 3));

        return VPClosePriceItemView.stock(
          onTap: (p0) {
            if (p0 == null) return;
            context.read<PlaceOrderCubit>().updatePrice(p0);
          },
          stock: stock,
          priceBuilder: (closePrice) {
            if (closePrice == null) return '-.-';

            return null;
          },
          styleBuilder: (closePrice, color) {
            if (closePrice == null) {
              return placeHolderColor;
            }

            return vpTextStyle.headineBold5
                .copyColor(color ?? vpColor.textTertiary)
                ?.copyWith(fontSize: transform(24, 12, t * 3, 3));
          },
        );
      },
    );
  }
}
