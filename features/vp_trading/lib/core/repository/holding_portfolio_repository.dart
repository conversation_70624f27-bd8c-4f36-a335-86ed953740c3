import 'package:vp_common/error/handle_error.dart';
import 'package:vp_trading/core/service/holding_portfolio_service.dart';
import 'package:vp_trading/model/derivatives_portfolio/derivatives_portfolio_model.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';

abstract class HoldingPortfolioRepository {
  Future<List<HoldingPortfolioStockModel>?> getHoldingPortfolio({
    String? accountId,
    required String symbol,
  });

  Future<DerivativesPortfolioModel?> getDerivativesPositions({
    required String accountId,
    required String symbol,
  });
}

class HoldingPortfolioRepositoryImpl extends HoldingPortfolioRepository {
  final HoldingPortfolioService holdingPortfolioService;

  HoldingPortfolioRepositoryImpl({required this.holdingPortfolioService});

  @override
  Future<List<HoldingPortfolioStockModel>?> getHoldingPortfolio({
    String? accountId,
    required String symbol,
  }) async {
    try {
      final response = await holdingPortfolioService.getHoldingPortfolio(
        accountId,
        symbol,
      );

      return response.data;
    } catch (err) {
      throw HandleError.from(err);
    }
  }

  @override
  Future<DerivativesPortfolioModel?> getDerivativesPositions({
    required String accountId,
    required String symbol,
  }) async {
    try {
      final response = await holdingPortfolioService.getDerivativesPositions(
        accountId,
        symbol,
      );
      return response.data;
    } catch (err) {
      throw HandleError.from(err);
    }
  }
}
