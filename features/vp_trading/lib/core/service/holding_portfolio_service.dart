import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/model/derivatives_portfolio/derivatives_portfolio_model.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';

part 'holding_portfolio_service.g.dart';

@RestApi()
abstract class HoldingPortfolioService {
  factory HoldingPortfolioService(Dio dio, {String baseUrl}) =
      _HoldingPortfolioService;

  @GET("/neo-inv-customer/public/v1/portfolio/stocks")
  Future<BaseResponse<List<HoldingPortfolioStockModel>>> getHoldingPortfolio(
    @Query('accountId') String? accountId,
    @Query('symbol') String symbol,
  );

  @GET("/neo-inv-customer/public/v1/portfolio/derivatives/positions")
  Future<BaseResponse<DerivativesPortfolioModel>> getDerivativesPositions(
    @Query('accountId') String accountId,
    @Query('symbol') String symbol,
  );
}
