import 'package:json_annotation/json_annotation.dart';

part 'derivatives_portfolio_model.g.dart';

@JsonSerializable()
class DerivativesPortfolioModel {
  @Json<PERSON>ey(name: 'openPositions')
  final List<DerivativesPositionModel>? openPositions;

  @Json<PERSON>ey(name: 'closePositions')
  final List<DerivativesPositionModel>? closePositions;

  const DerivativesPortfolioModel({this.openPositions, this.closePositions});

  factory DerivativesPortfolioModel.fromJson(Map<String, dynamic> json) =>
      _$DerivativesPortfolioModelFromJson(json);

  Map<String, dynamic> toJson() => _$DerivativesPortfolioModelToJson(this);
}

@JsonSerializable()
class DerivativesPositionModel {
  @JsonKey(name: 'accountId')
  final String? accountId;

  @JsonKey(name: 'productTypeCd')
  final String? productTypeCd;

  @Json<PERSON>ey(name: 'vwap')
  final double? vwap;

  @Json<PERSON>ey(name: 'vrImAmt')
  final double? vrImAmt;

  @JsonKey(name: 'vrDebtVmAmt')
  final double? vrDebtVmAmt;

  @JsonKey(name: 'vmAmt')
  final double? vmAmt;

  @JsonKey(name: 'totalPLAmt')
  final double? totalPLAmt;

  @JsonKey(name: 'symbol')
  final String? symbol;

  @JsonKey(name: 'quoteId')
  final String? quoteId;

  @JsonKey(name: 'qty')
  final int? qty;

  @JsonKey(name: 'priceSecured')
  final double? priceSecured;

  @JsonKey(name: 'position')
  final String? position;

  @JsonKey(name: 'pendingSQty')
  final int? pendingSQty;

  @JsonKey(name: 'pendingLQty')
  final int? pendingLQty;

  @JsonKey(name: 'percentNonrPLAmt')
  final int? percentNonrPLAmt;

  @JsonKey(name: 'nonrPlAmt')
  final double? nonrPlAmt;

  @JsonKey(name: 'lastChange')
  final DateTime? lastChange;

  @JsonKey(name: 'isTpsl')
  final String? isTpsl;

  @JsonKey(name: 'isNet')
  final String? isNet;

  @JsonKey(name: 'isClose')
  final String? isClose;

  @JsonKey(name: 'dsp')
  final double? dsp;

  @JsonKey(name: 'diff')
  final int? diff;

  @JsonKey(name: 'custodyCd')
  final String? custodyCd;

  @JsonKey(name: 'nValue')
  final double? nValue;

  const DerivativesPositionModel({
    this.accountId,
    this.productTypeCd,
    this.vwap,
    this.vrImAmt,
    this.vrDebtVmAmt,
    this.vmAmt,
    this.totalPLAmt,
    this.symbol,
    this.quoteId,
    this.qty,
    this.priceSecured,
    this.position,
    this.pendingSQty,
    this.pendingLQty,
    this.percentNonrPLAmt,
    this.nonrPlAmt,
    this.lastChange,
    this.isTpsl,
    this.isNet,
    this.isClose,
    this.dsp,
    this.diff,
    this.custodyCd,
    this.nValue,
  });

  factory DerivativesPositionModel.fromJson(Map<String, dynamic> json) =>
      _$DerivativesPositionModelFromJson(json);

  Map<String, dynamic> toJson() => _$DerivativesPositionModelToJson(this);
}

// Helper function to parse the root array response
List<DerivativesPortfolioModel> parseDerivativesPortfolioList(
  List<dynamic> json,
) {
  return json
      .map(
        (item) =>
            DerivativesPortfolioModel.fromJson(item as Map<String, dynamic>),
      )
      .toList();
}

extension DerivativesPositionModelExtension on DerivativesPositionModel {
// Nếu istpsl = Y → Hiển thị button Cài đặt giá SL/TP
// Nếu istpsl = N→ Hiển thị thông tin lệnh TPSL chờ kích hoạt
  bool get canSetStopLoss => isTpsl == 'Y';
  bool get hasPendingTpslOrders => isTpsl == 'N';
  bool get isLongPosition => qty != null && qty! > 0;
 
}
