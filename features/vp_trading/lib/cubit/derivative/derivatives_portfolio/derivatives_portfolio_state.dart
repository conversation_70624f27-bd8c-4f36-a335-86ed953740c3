part of 'derivatives_portfolio_cubit.dart';

class DerivativesPortfolioState extends Equatable {
  final ApiStatus apiStatus;
  final DerivativesPortfolioModel? data;
  final String? errorMessage;

  const DerivativesPortfolioState({
    required this.apiStatus,
    this.data,
    this.errorMessage,
  });

  DerivativesPortfolioState copyWith({
    ApiStatus? apiStatus,
    DerivativesPortfolioModel? data,
    String? errorMessage,
  }) {
    return DerivativesPortfolioState(
      apiStatus: apiStatus ?? this.apiStatus,
      data: data ?? this.data,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [apiStatus, data, errorMessage];

  /// Helper method to get open positions
  List<DerivativesPositionModel> get openPositions => data?.openPositions ?? [];

  /// Helper method to get closed positions
  List<DerivativesPositionModel> get closedPositions =>
      data?.closePositions ?? [];

  /// Helper method to check if there are any open positions
  bool get hasOpenPositions => openPositions.isNotEmpty;

  /// Helper method to check if there are any closed positions
  bool get hasClosedPositions => closedPositions.isNotEmpty;

  /// Helper method to check if data is loading
  bool get isLoading => apiStatus.isLoading;

  /// Helper method to check if data loaded successfully
  bool get isSuccess => apiStatus.isDone;

  /// Helper method to check if there's an error
  bool get hasError => apiStatus.isError;
}
