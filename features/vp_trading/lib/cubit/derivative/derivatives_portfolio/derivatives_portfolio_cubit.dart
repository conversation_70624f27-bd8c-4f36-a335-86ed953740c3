import 'package:equatable/equatable.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/holding_portfolio_repository.dart';
import 'package:vp_trading/model/derivatives_portfolio/derivatives_portfolio_model.dart';

part 'derivatives_portfolio_state.dart';

class DerivativesPortfolioCubit extends Cubit<DerivativesPortfolioState> {
  DerivativesPortfolioCubit({this.accountId = '', this.symbol = ''})
    : super(DerivativesPortfolioState(apiStatus: ApiStatus.initial()));

  final HoldingPortfolioRepository _repository =
      GetIt.instance<HoldingPortfolioRepository>();
  final String accountId;
  final String symbol;

  Future<void> fetchPortfolioData() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      DerivativesPortfolioModel? data;

      if (accountId.isNotEmpty && symbol.isNotEmpty) {
        data = await _repository.getDerivativesPositions(
          accountId: accountId,
          symbol: symbol,
        );
      }

      // If no data from repository, use mock data for demonstration
      data ??= DerivativesPortfolioModel(
        openPositions: [
          DerivativesPositionModel(
            symbol: 'VN30F2412',
            qty: 2,
            position: 'L',
            priceSecured: 1033.5,
            totalPLAmt: 2911000.0,
            isTpsl: 'Y', // Can set SL/TP - shows button
            vmAmt: 2500.0,
            lastChange: DateTime.now(),
          ),
          DerivativesPositionModel(
            symbol: 'VN30F2501',
            qty: 5,
            position: 'S',
            priceSecured: 1280.0,
            totalPLAmt: -1500000.0,
            isTpsl: 'N', // Has pending TPSL orders - shows info
            vmAmt: -1200.0,
            lastChange: DateTime.now(),
          ),
          DerivativesPositionModel(
            symbol: 'VN30F2503',
            qty: 3,
            position: 'L',
            priceSecured: 1150.0,
            totalPLAmt: 850000.0,
            isTpsl: 'Y', // Can set SL/TP - shows button
            vmAmt: 1800.0,
            lastChange: DateTime.now(),
          ),
        ],
        closePositions: [],
      );

      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          data: data,
          errorMessage: null,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          apiStatus: ApiStatus.error(error),
          errorMessage: error.toString(),
        ),
      );
    }
  }

  Future<void> refreshPortfolioData() async {
    await fetchPortfolioData();
  }

  Future<void> updateParameters({
    String? newAccountId,
    String? newSymbol,
  }) async {
    await fetchPortfolioData();
  }

  /// Handles setting SL/TP for a position
  ///
  /// [position] The position to set SL/TP for
  Future<void> setStopLossTakeProfit(DerivativesPositionModel position) async {
    // TODO: Implement SL/TP setting logic
    // This will navigate to SL/TP setting screen or show dialog
    // For now, just log the action
  }

  /// Handles viewing pending TPSL orders for a position
  ///
  /// [position] The position to view pending orders for
  Future<void> viewPendingTpslOrders(DerivativesPositionModel position) async {
    // TODO: Implement pending TPSL orders viewing logic
    // This will navigate to pending orders screen or show dialog
    // For now, just log the action
  }
}
