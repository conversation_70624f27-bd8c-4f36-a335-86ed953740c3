part of 'stock_board_item_view.dart';

class _ClosePriceView extends StatelessWidget {
  const _ClosePriceView({required this.item});

  final StockInfoModel item;

  String getText(VPStockInfoData? data) {
    final closePrice = StockBoardItemHelper.getClosePrice(
      item: item,
      data: data,
    );

    switch (item.stockType) {
      case StockType.FUVN30:
      case StockType.FUVN100:
      case StockType.FUGB:
        return StockBoardUIHelper.getText(
          item: item,
          socketData: data,
          value: FormatUtils.formatNumberWithOneDecimal(closePrice),
        );
      default:
        return StockBoardUIHelper.getText(
          item: item,
          socketData: data,
          value: FormatUtils.formatClosePrice(closePrice, trimZero: false),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return VPSocketInvestmentBuilder<VPStockInfoData>(
      symbol: item.symbol,
      channel: VPSocketChannel.stockInfo.name,
      buildWhen:
          (preData, data) => StockBoardItemHelper.buildWhen(
            type: StockInfoFieldType.closePrice,
            item: item,
            preData: preData,
            data: data,
          ),
      builder: (_, preData, data, child) {
        final closePrice = StockBoardItemHelper.getClosePrice(
          item: item,
          data: data,
        );

        final textColor = StockBoardUIHelper.getTextColor(
          item: item,
          data: data,
        );

        return VPFlashingColorView(
          key: ValueKey(item.symbol),
          data: closePrice,
          flashColor: data.color ?? item.color ?? Colors.red,
          builder: (child, status) {
            return Text(
              getText(data),
              textAlign: TextAlign.end,
              style: vpTextStyle.subtitle14.copyColor(
                status == FlashingStatus.start ? vpColor.textWhite : textColor,
              ),
            );
          },
        );
      },
    );
  }
}
